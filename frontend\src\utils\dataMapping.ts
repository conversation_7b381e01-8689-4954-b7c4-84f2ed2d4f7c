// Data mapping utilities for integrating backend data with Mai Valley ID card components
// This file contains general mapping utilities and Layout2/Layout3 specific mappings

import { PersonData, SchoolData } from "../components/Card/types";

// Interface for Layout2/Layout3 ID Card component props
export interface IdCardProps {
  id?: number;
  schoolName: string;
  motto: string;
  subname: string;
  contact: string;
  validity: string;
  logo: string;
  image: string;
  studentName: string;
  studentclass?: string;
  roll: string;
  idNumber: string;
  address: string;
  phone: string;
  principalSignature: string;
  personType: "student" | "staff" | "non_teaching";
  parentName?: string;
  department?: string;
  dateOfBirth?: string;
  bgColor?: "orange" | "blue" | "green";
  fieldVisibilityConfig?: {
    name: boolean;
    class: boolean;
    section: boolean;
    roll_number: boolean;
    parents_name: boolean;
    contact_no: boolean;
    department: boolean;
    designation: boolean;
    date_of_birth: boolean;
  };
}

// API base URL for constructing asset URLs
const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

// Default values for Layout2/Layout3
const LAYOUT2_3_DEFAULT_VALUES = {
  motto: '"Leading towards brightness"',
  validity: "2083/01/15",
  principalSignature: "", // Empty string to avoid external URL issues
  defaultLogo: "", // Empty string to avoid external URL issues
  defaultPhoto: "", // Empty string to avoid external URL issues - ProfileImage will show initials
};

/**
 * Validate and construct photo URL with fallback.
 */
export const getPhotoUrl = (photoPath?: string): string => {
  if (!photoPath || photoPath.trim() === "") {
    return ""; // Return empty string - ProfileImage component will show initials
  }

  // Check if it's already a full URL (but avoid external URLs for security)
  if (photoPath.startsWith("http://") || photoPath.startsWith("https://")) {
    // Only allow same-origin URLs or our API URLs
    if (
      photoPath.includes(API_BASE_URL) ||
      photoPath.startsWith(window.location.origin)
    ) {
      return photoPath;
    }
    // For external URLs, return empty string to show initials instead
    console.warn(`External photo URL blocked for security: ${photoPath}`);
    return "";
  }

  // Add cache-busting parameter to prevent browser caching issues
  const cacheBuster = `?t=${Date.now()}`;

  // Construct API URL with cache buster
  return `${API_BASE_URL}/api/cards/photos/${photoPath}${cacheBuster}`;
};

/**
 * Validate and construct logo URL with fallback
 */
export const getLogoUrl = (logoPath?: string): string => {
  if (!logoPath || logoPath.trim() === "") {
    return ""; // Return empty string - components will handle missing logo gracefully
  }

  // Check if it's already a full URL (but avoid external URLs for security)
  if (logoPath.startsWith("http://") || logoPath.startsWith("https://")) {
    // Only allow same-origin URLs or our API URLs
    if (
      logoPath.includes(API_BASE_URL) ||
      logoPath.startsWith(window.location.origin)
    ) {
      return logoPath;
    }
    // For external URLs, return empty string
    console.warn(`External logo URL blocked for security: ${logoPath}`);
    return "";
  }

  // Add cache-busting parameter to prevent browser caching issues
  const cacheBuster = `?t=${Date.now()}`;

  // Construct API URL using the correct logos endpoint with cache buster
  return `${API_BASE_URL}/api/cards/logos/${logoPath}${cacheBuster}`;
};

/**
 * Validate and construct principal signature URL with fallback
 */
export const getPrincipalSignatureUrl = (signaturePath?: string): string => {
  if (!signaturePath || signaturePath.trim() === "") {
    return ""; // Return empty string - components will handle missing signature gracefully
  }

  // Check if it's already a full URL (but avoid external URLs for security)
  if (
    signaturePath.startsWith("http://") ||
    signaturePath.startsWith("https://")
  ) {
    // Only allow same-origin URLs or our API URLs
    if (
      signaturePath.includes(API_BASE_URL) ||
      signaturePath.startsWith(window.location.origin)
    ) {
      return signaturePath;
    }
    // For external URLs, return empty string
    console.warn(
      `External signature URL blocked for security: ${signaturePath}`
    );
    return "";
  }

  // Add cache-busting parameter to prevent browser caching issues
  const cacheBuster = `?t=${Date.now()}`;

  // Use the proper API endpoint with CORS headers instead of direct uploads path
  return `${API_BASE_URL}/api/cards/logos/${signaturePath}${cacheBuster}`;
};

/**
 * Transform backend PersonData and SchoolData into Layout2/Layout3 IdCardProps format
 */
export const transformToLayout2_3IdCardProps = (
  person: PersonData,
  school: SchoolData,
  customValues?: Partial<IdCardProps>
): IdCardProps => {
  // Construct photo, logo, and signature URLs with proper fallbacks
  const photoUrl = getPhotoUrl(person.photo_path);
  const logoUrl = getLogoUrl(school.logo_path);
  const principalSignatureUrl = getPrincipalSignatureUrl(school.signature_path);

  // Format school name - use full name for Layout2
  const formatSchoolName = (name: string): string => {
    return name.toUpperCase();
  };

  // Format school subname - use address for Layout2/Layout3 location field
  const formatSchoolSubname = (address: string): string => {
    // For Layout2/Layout3, we want to use the address as the location
    return address;
  };

  // Generate ID number if not available
  const generateIdNumber = (personId: string, schoolName: string): string => {
    if (personId) return personId;

    // Create ID from school initials and person ID
    const initials = schoolName
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase();

    return `${initials}-${person.id.toString().padStart(4, "0")}`;
  };

  // Format validity date from school or use default
  const validityDate = school.validity_date
    ? new Date(school.validity_date)
        .toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        })
        .replace(/\//g, "/")
    : LAYOUT2_3_DEFAULT_VALUES.validity;

  const baseProps: IdCardProps = {
    id: person.id,
    schoolName: formatSchoolName(school.name),
    motto: LAYOUT2_3_DEFAULT_VALUES.motto,
    subname: formatSchoolSubname(school.address),
    contact: school.phone,
    validity: validityDate,
    logo: logoUrl,
    image: photoUrl,
    studentName: person.name,
    studentclass: person.class || "",
    roll: person.roll_number || "00",
    idNumber: generateIdNumber(person.person_id, school.name),
    address: person.address || "",
    phone: person.contact_no || school.phone,
    principalSignature: principalSignatureUrl,
    personType: person.type,
    parentName: person.parents_name,
    department: person.department || person.class,
    dateOfBirth: person.date_of_birth,
  };

  // Apply any custom overrides
  return { ...baseProps, ...customValues };
};

/**
 * Transform multiple persons into Layout2/Layout3 IdCardProps array
 */
export const transformPersonsToLayout2_3IdCards = (
  persons: PersonData[],
  school: SchoolData,
  customValues?: Partial<IdCardProps>
): IdCardProps[] => {
  return persons.map((person) =>
    transformToLayout2_3IdCardProps(person, school, customValues)
  );
};

/**
 * Filter persons by type and transform to Layout2/Layout3 IdCardProps
 */
export const getLayout2_3IdCardsByType = (
  persons: PersonData[],
  school: SchoolData,
  type: "student" | "staff" | "non_teaching",
  customValues?: Partial<IdCardProps>
): IdCardProps[] => {
  const filteredPersons = persons.filter((person) => person.type === type);
  return transformPersonsToLayout2_3IdCards(
    filteredPersons,
    school,
    customValues
  );
};

/**
 * Get color coding for Layout2 based on person type
 */
export const getColorCodingForPerson = (
  type: string
): "orange" | "blue" | "green" => {
  switch (type) {
    case "student":
      return "orange";
    case "staff":
      return "blue";
    case "non_teaching":
      return "green";
    default:
      return "orange";
  }
};

/**
 * Transform persons with color coding for Layout2/Layout3
 */
export const transformPersonsWithColorCoding = (
  persons: PersonData[],
  school: SchoolData,
  customValues?: Partial<IdCardProps>
): (IdCardProps & { bgColor: "orange" | "blue" | "green" })[] => {
  return persons.map((person) => ({
    ...transformToLayout2_3IdCardProps(person, school, customValues),
    bgColor: getColorCodingForPerson(person.type),
  }));
};

const layout2_3DataMapping = {
  transformToLayout2_3IdCardProps,
  transformPersonsToLayout2_3IdCards,
  getLayout2_3IdCardsByType,
  getColorCodingForPerson,
  transformPersonsWithColorCoding,
  getPhotoUrl,
  getLogoUrl,
  getPrincipalSignatureUrl,
};

// Backward compatibility exports (deprecated - use layout-specific imports)
export const transformToIdCardProps = transformToLayout2_3IdCardProps;
export const transformPersonsToIdCards = transformPersonsToLayout2_3IdCards;
export const getIdCardsByType = getLayout2_3IdCardsByType;

export default layout2_3DataMapping;
