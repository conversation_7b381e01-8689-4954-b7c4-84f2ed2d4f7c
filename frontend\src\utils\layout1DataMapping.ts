// Data mapping utilities specifically for Layout1 ID card components
// Layout1 has unique structure with decorative patterns, specific field layout, and field visibility controls

import { PersonData, SchoolData } from "../components/Card/types";
import {
  IdCardProps,
  IDCardFieldVisibility,
} from "../components/IdCard/layouts/Layout1/types";

// API base URL for constructing asset URLs
const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

// Default values for Layout1 specific requirements
export const LAYOUT1_DEFAULT_VALUES = {
  motto: '"Leading towards brightness"',
  validity: "2083/01/15",
  principalSignature: "", // Empty string to avoid external URL issues
  defaultLogo: "", // Empty string to avoid external URL issues
  defaultPhoto: "", // Empty string to avoid external URL issues - ProfileImage will show initials
};

// Default field visibility configuration for Layout1
export const DEFAULT_FIELD_VISIBILITY: IDCardFieldVisibility = {
  name: true,
  class: true,
  section: true,
  roll_number: true,
  parents_name: true,
  contact_no: true,
  department: true,
  designation: true,
  address: true,
  date_of_birth: true,
};

/**
 * Validate and construct photo URL with fallback for Layout1.
 */
export const getLayout1PhotoUrl = (photoPath?: string): string => {
  if (!photoPath || photoPath.trim() === "") {
    return ""; // Return empty string - ProfileImage component will show initials
  }

  // Check if it's already a full URL (but avoid external URLs for security)
  if (photoPath.startsWith("http://") || photoPath.startsWith("https://")) {
    // Only allow same-origin URLs or our API URLs
    if (
      photoPath.includes(API_BASE_URL) ||
      photoPath.startsWith(window.location.origin)
    ) {
      return photoPath;
    }
    // For external URLs, return empty string to show initials instead
    console.warn(`External photo URL blocked for security: ${photoPath}`);
    return "";
  }

  // Add cache-busting parameter to prevent browser caching issues
  const cacheBuster = `?t=${Date.now()}`;

  // Construct API URL with cache buster
  return `${API_BASE_URL}/api/cards/photos/${photoPath}${cacheBuster}`;
};

/**
 * Validate and construct logo URL with fallback for Layout1
 */
export const getLayout1LogoUrl = (logoPath?: string): string => {
  if (!logoPath || logoPath.trim() === "") {
    return ""; // Return empty string - components will handle missing logo gracefully
  }

  // Check if it's already a full URL (but avoid external URLs for security)
  if (logoPath.startsWith("http://") || logoPath.startsWith("https://")) {
    // Only allow same-origin URLs or our API URLs
    if (
      logoPath.includes(API_BASE_URL) ||
      logoPath.startsWith(window.location.origin)
    ) {
      return logoPath;
    }
    // For external URLs, return empty string
    console.warn(`External logo URL blocked for security: ${logoPath}`);
    return "";
  }

  // Add cache-busting parameter to prevent browser caching issues
  const cacheBuster = `?t=${Date.now()}`;

  // Construct API URL using the correct logos endpoint with cache buster
  return `${API_BASE_URL}/api/cards/logos/${logoPath}${cacheBuster}`;
};

/**
 * Validate and construct principal signature URL with fallback for Layout1
 */
export const getLayout1PrincipalSignatureUrl = (
  signaturePath?: string
): string => {
  if (!signaturePath || signaturePath.trim() === "") {
    return ""; // Return empty string - components will handle missing signature gracefully
  }

  // Check if it's already a full URL (but avoid external URLs for security)
  if (
    signaturePath.startsWith("http://") ||
    signaturePath.startsWith("https://")
  ) {
    // Only allow same-origin URLs or our API URLs
    if (
      signaturePath.includes(API_BASE_URL) ||
      signaturePath.startsWith(window.location.origin)
    ) {
      return signaturePath;
    }
    // For external URLs, return empty string
    console.warn(
      `External signature URL blocked for security: ${signaturePath}`
    );
    return "";
  }

  // Add cache-busting parameter to prevent browser caching issues
  const cacheBuster = `?t=${Date.now()}`;

  // Use the proper API endpoint with CORS headers instead of direct uploads path
  return `${API_BASE_URL}/api/cards/logos/${signaturePath}${cacheBuster}`;
};

/**
 * Format school name for Layout1 header - handles long names intelligently
 */
const formatLayout1SchoolName = (name: string): string => {
  const upperName = name.toUpperCase();

  // If name is too long, try to format it better for the header
  if (upperName.length > 20) {
    // Try to break at natural word boundaries
    const words = upperName.split(" ");

    // If it's a common pattern like "NAME BOARDING SCHOOL", format it nicely
    if (words.includes("BOARDING") && words.includes("SCHOOL")) {
      const nameWords = [];
      const suffixWords = [];
      let foundBoarding = false;

      for (const word of words) {
        if (word === "BOARDING" || foundBoarding) {
          foundBoarding = true;
          suffixWords.push(word);
        } else {
          nameWords.push(word);
        }
      }

      // Return formatted as "NAME\nBOARDING SCHOOL" for better display
      return `${nameWords.join(" ")}\n${suffixWords.join(" ")}`;
    }

    // Special handling for common school name patterns
    if (words.includes("HIGH") && words.includes("SCHOOL")) {
      const nameWords = [];
      const suffixWords = [];
      let foundHigh = false;

      for (const word of words) {
        if (word === "HIGH" || foundHigh) {
          foundHigh = true;
          suffixWords.push(word);
        } else {
          nameWords.push(word);
        }
      }

      return `${nameWords.join(" ")}\n${suffixWords.join(" ")}`;
    }

    // For other long names, try to break at the middle word
    if (words.length >= 3) {
      const midPoint = Math.ceil(words.length / 2);
      const firstLine = words.slice(0, midPoint).join(" ");
      const secondLine = words.slice(midPoint).join(" ");
      return `${firstLine}\n${secondLine}`;
    }
  }

  return upperName;
};

/**
 * Format school name for Layout1 header - single line version (fallback)
 */
export const formatLayout1SchoolNameSingleLine = (name: string): string => {
  return name.toUpperCase();
};

/**
 * Format school subname for Layout1 - uses school address with length optimization
 */
const formatLayout1SchoolSubname = (address: string): string => {
  // If address is too long, try to shorten it intelligently
  if (address.length > 30) {
    // Common abbreviations for addresses
    const abbreviations: { [key: string]: string } = {
      STREET: "ST.",
      ROAD: "RD.",
      AVENUE: "AVE.",
      DISTRICT: "DIST.",
      MUNICIPALITY: "MUN.",
      KATHMANDU: "KTM",
      LALITPUR: "LTP",
      BHAKTAPUR: "BKT",
    };

    let shortened = address.toUpperCase();
    for (const [full, abbrev] of Object.entries(abbreviations)) {
      shortened = shortened.replace(new RegExp(`\\b${full}\\b`, "g"), abbrev);
    }

    return shortened;
  }

  return address.toUpperCase();
};

/**
 * Get optimal school name formatting based on length and content
 */
export const getOptimalSchoolNameFormat = (
  name: string
): {
  formatted: string;
  isMultiLine: boolean;
  recommendedFontSize: string;
} => {
  const formatted = formatLayout1SchoolName(name);
  const isMultiLine = formatted.includes("\n");

  // Recommend font size based on length and line count
  let recommendedFontSize = "text-base"; // Default

  if (isMultiLine) {
    recommendedFontSize = "text-sm"; // Smaller for multi-line
  } else if (name.length > 20) {
    recommendedFontSize = "text-sm"; // Smaller for long single line
  } else if (name.length > 15) {
    recommendedFontSize = "text-base"; // Normal
  }

  return {
    formatted,
    isMultiLine,
    recommendedFontSize,
  };
};

/**
 * Helper function for CardHeader component to render school name properly
 */
export const renderSchoolNameForHeader = (
  name: string
): {
  lines: string[];
  className: string;
  shouldWrap: boolean;
} => {
  const { formatted, isMultiLine } = getOptimalSchoolNameFormat(name);

  const lines = isMultiLine ? formatted.split("\n") : [formatted];

  // Determine if we should allow wrapping or use nowrap
  const shouldWrap = isMultiLine || name.length > 20;

  // Build className based on formatting needs - optimized for 204px card width
  let className = `font-bold text-center`;

  if (isMultiLine) {
    // For multi-line, use smaller font and tight spacing
    className += " text-xs leading-tight -mt-1";
  } else if (name.length > 20) {
    // For long single line, use smaller font
    className += " text-xs -mt-1";
  } else if (name.length > 15) {
    // For medium length, use small font
    className += " text-sm -mt-1";
  } else {
    // For short names, use base font
    className += " text-base -mt-1";
  }

  return {
    lines,
    className,
    shouldWrap,
  };
};

/**
 * Generate ID number for Layout1 if not available
 */
const generateLayout1IdNumber = (
  personId: string,
  schoolName: string,
  personDbId: number
): string => {
  if (personId) return personId;

  // Create ID from school initials and person ID
  const initials = schoolName
    .split(" ")
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase();

  return `${initials}-${personDbId.toString().padStart(4, "0")}`;
};

/**
 * Transform backend PersonData and SchoolData into Layout1 IdCardProps format
 */
export const transformToLayout1IdCardProps = (
  person: PersonData,
  school: SchoolData,
  fieldVisibilityConfig?: IDCardFieldVisibility,
  customValues?: Partial<IdCardProps>
): IdCardProps => {
  // Construct photo, logo, and signature URLs with proper fallbacks
  const photoUrl = getLayout1PhotoUrl(person.photo_path);
  const logoUrl = getLayout1LogoUrl(school.logo_path);
  const principalSignatureUrl = getLayout1PrincipalSignatureUrl(
    school.signature_path
  );

  // Format validity date from school or use default
  const validityDate = school.validity_date
    ? new Date(school.validity_date)
        .toLocaleDateString("en-CA", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        })
        .replace(/\//g, "/")
    : LAYOUT1_DEFAULT_VALUES.validity;

  const baseProps: IdCardProps = {
    id: person.id,
    schoolName: formatLayout1SchoolName(school.name),
    motto: LAYOUT1_DEFAULT_VALUES.motto,
    subname: formatLayout1SchoolSubname(school.address),
    contact: school.phone,
    validity: validityDate,
    logo: logoUrl,
    image: photoUrl,
    studentName: person.name,
    studentclass: person.class || "",
    roll: person.roll_number || "00",
    idNumber: generateLayout1IdNumber(person.person_id, school.name, person.id),
    address: person.address || "",
    phone: person.contact_no || school.phone,
    principalSignature: principalSignatureUrl,
    personType: person.type,
    parentName: person.parents_name,
    department: person.department || person.class,
    fieldVisibilityConfig: fieldVisibilityConfig || DEFAULT_FIELD_VISIBILITY,
  };

  // Apply any custom overrides
  return { ...baseProps, ...customValues };
};

/**
 * Transform multiple persons into Layout1 IdCardProps array
 */
export const transformPersonsToLayout1IdCards = (
  persons: PersonData[],
  school: SchoolData,
  fieldVisibilityConfig?: IDCardFieldVisibility,
  customValues?: Partial<IdCardProps>
): IdCardProps[] => {
  return persons.map((person) =>
    transformToLayout1IdCardProps(
      person,
      school,
      fieldVisibilityConfig,
      customValues
    )
  );
};

/**
 * Filter persons by type and transform to Layout1 IdCardProps
 */
export const getLayout1IdCardsByType = (
  persons: PersonData[],
  school: SchoolData,
  type: "student" | "staff" | "non_teaching",
  fieldVisibilityConfig?: IDCardFieldVisibility,
  customValues?: Partial<IdCardProps>
): IdCardProps[] => {
  const filteredPersons = persons.filter((person) => person.type === type);
  return transformPersonsToLayout1IdCards(
    filteredPersons,
    school,
    fieldVisibilityConfig,
    customValues
  );
};

/**
 * Create field visibility configuration for Layout1
 */
export const createLayout1FieldVisibility = (
  overrides?: Partial<IDCardFieldVisibility>
): IDCardFieldVisibility => {
  return { ...DEFAULT_FIELD_VISIBILITY, ...overrides };
};

// Export all Layout1 specific mapping functions
const layout1DataMapping = {
  transformToLayout1IdCardProps,
  transformPersonsToLayout1IdCards,
  getLayout1IdCardsByType,
  createLayout1FieldVisibility,
  getLayout1PhotoUrl,
  getLayout1LogoUrl,
  getLayout1PrincipalSignatureUrl,
  getOptimalSchoolNameFormat,
  renderSchoolNameForHeader,
  formatLayout1SchoolNameSingleLine,
  DEFAULT_FIELD_VISIBILITY,
  LAYOUT1_DEFAULT_VALUES,
};

export default layout1DataMapping;
