import React from "react";
import { Icon } from "@iconify/react";
import { SchoolCardProps } from "../types";

const SchoolCard: React.FC<SchoolCardProps> = ({
  school,
  uploadingLogo,
  onEdit,
  onDelete,
  onLogoUpload,
  onSignatureUpload,
  onValidityDateUpdate,
}) => {
  const API_BASE_URL =
    process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onLogoUpload(school.id, file);
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center overflow-hidden">
            {school.logo_path ? (
              <img
                src={`${API_BASE_URL}/api/cards/logos/${school.logo_path}`}
                alt={`${school.name} logo`}
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-blue-600 font-semibold text-lg">
                {school.name.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{school.name}</h3>
            <div className="mt-1">
              <label className="cursor-pointer text-xs text-blue-600 hover:text-blue-800">
                {uploadingLogo
                  ? "Uploading..."
                  : school.logo_path
                  ? "Change Logo"
                  : "Upload Logo"}
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  disabled={uploadingLogo}
                  onChange={handleLogoChange}
                />
              </label>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-2 text-sm text-gray-600 mb-4">
        <div className="flex items-start space-x-2">
          <span>📍</span>
          <span>{school.address}</span>
        </div>
        <div className="flex items-center space-x-2">
          <span>📞</span>
          <span>{school.phone}</span>
        </div>
        {school.email && (
          <div className="flex items-center space-x-2">
            <span>✉️</span>
            <span>{school.email}</span>
          </div>
        )}
        {school.validity_date && (
          <div className="flex items-center space-x-2">
            <span>📅</span>
            <span>
              Valid Until: {new Date(school.validity_date).toLocaleDateString()}
            </span>
          </div>
        )}
      </div>

      {/* Validity Date Section */}
      <div className="mb-4 p-3 bg-yellow-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            ID Card Validity
          </span>
          {onValidityDateUpdate && (
            <button
              onClick={() => onValidityDateUpdate(school)}
              className="text-xs text-orange-600 hover:text-orange-800 font-medium flex items-center space-x-1"
            >
              <Icon icon="mdi:calendar-edit" className="h-3 w-3" />
              <span>Update</span>
            </button>
          )}
        </div>
        <div className="text-sm text-gray-600">
          {school.validity_date ? (
            <span>
              Valid until: {new Date(school.validity_date).toLocaleDateString()}
            </span>
          ) : (
            <span className="text-gray-400">No validity date set</span>
          )}
        </div>
      </div>

      {/* Principal Signature Section */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Principal Signature
          </span>
          {onSignatureUpload && (
            <button
              onClick={() => onSignatureUpload(school)}
              className="text-xs text-purple-600 hover:text-purple-800 font-medium flex items-center space-x-1"
            >
              <Icon icon="mdi:upload" className="h-3 w-3" />
              <span>Upload</span>
            </button>
          )}
        </div>
        <div className="w-full h-16 bg-white rounded border-2 border-gray-200 flex items-center justify-center overflow-hidden">
          {school.signature_path ? (
            <img
              src={`${API_BASE_URL}/uploads/school-assets/${school.signature_path}`}
              alt="Principal signature"
              className="max-w-full max-h-full object-contain"
            />
          ) : (
            <div className="text-center">
              <Icon
                icon="mdi:signature-freehand"
                className="h-6 w-6 text-gray-400 mx-auto mb-1"
              />
              <span className="text-xs text-gray-400">No signature</span>
            </div>
          )}
        </div>
      </div>

      <div className="flex space-x-2">
        <button
          onClick={() => onEdit(school)}
          className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-md text-sm hover:bg-blue-100"
        >
          Edit
        </button>
        <button
          onClick={() => onDelete(school)}
          className="flex-1 bg-red-50 text-red-600 px-3 py-2 rounded-md text-sm hover:bg-red-100"
        >
          Delete
        </button>
      </div>
    </div>
  );
};

export default SchoolCard;
