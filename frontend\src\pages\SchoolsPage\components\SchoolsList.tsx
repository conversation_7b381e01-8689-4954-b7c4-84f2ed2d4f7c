import React, { useState } from "react";
import SchoolCard from "./SchoolCard";
import { SchoolsListProps } from "../types";
import { MasterTable } from "../../../components/Table/MasterTable";
import {
  ColumnConfig,
  TableActionConfig,
  TableData,
} from "../../../components/Table/types";
import { School } from "../types";

const SchoolsList: React.FC<SchoolsListProps> = ({
  schools,
  uploadingLogo,
  onEdit,
  onDelete,
  onLogoUpload,
  onSignatureUpload,
  onValidityDateUpdate,
  onCreateFirst,
}) => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Transform School to TableData format
  const transformSchoolToTableData = (school: School): TableData<School> => ({
    _id: school.id.toString(),
    ...school,
  });

  // Table configuration for MasterTable
  const tableColumns: ColumnConfig<School>[] = [
    {
      key: "logo_path",
      header: "Logo",
      width: "80px",
      render: (value, row) => (
        <div className="flex items-center space-x-2">
          <div className="w-10 h-10 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
            {value ? (
              <img
                src={`${
                  process.env.REACT_APP_API_URL ||
                  "https://print-api.webstudiomatrix.com"
                }/api/cards/logos/${value}?t=${Date.now()}`}
                alt={`${row.name} logo`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                  target.nextElementSibling!.classList.remove("hidden");
                }}
              />
            ) : null}
            <div className={`text-xs text-gray-500 ${value ? "hidden" : ""}`}>
              🏫
            </div>
          </div>
          <div className="flex items-center text-xs">
            <span
              className={`w-2 h-2 rounded-full mr-1 ${
                value ? "bg-green-400" : "bg-gray-300"
              }`}
            />
            {value ? "✓" : "✗"}
          </div>
        </div>
      ),
    },
    {
      key: "name",
      header: "School Name",
      sortable: true,
      render: (_value, row) => (
        <div>
          <div className="font-medium text-gray-900">{row.name}</div>
          <div className="text-sm text-gray-500">{row.address}</div>
        </div>
      ),
    },
    {
      key: "phone",
      header: "Contact",
      render: (_value, row) => (
        <div>
          <div className="text-sm text-gray-900">{row.phone}</div>
          {row.email && (
            <div className="text-sm text-gray-500">{row.email}</div>
          )}
        </div>
      ),
    },
    {
      key: "signature_path",
      header: "Principal Signature",
      width: "150px",
      render: (value, row) => (
        <div className="flex items-center space-x-2">
          <div className="w-16 h-8 bg-gray-50 rounded border flex items-center justify-center overflow-hidden">
            {value ? (
              <img
                src={`${
                  process.env.REACT_APP_API_URL || "http://localhost:3000"
                }/uploads/school-assets/${value}?t=${Date.now()}`}
                alt="Principal signature"
                className="max-w-full max-h-full object-contain"
              />
            ) : (
              <span className="text-xs text-gray-400">No signature</span>
            )}
          </div>
          <div className="flex items-center text-xs">
            <span
              className={`w-2 h-2 rounded-full mr-1 ${
                value ? "bg-purple-400" : "bg-gray-300"
              }`}
            />
            {value ? "✓" : "✗"}
          </div>
        </div>
      ),
    },
    {
      key: "validity_date",
      header: "Validity Date",
      width: "120px",
      render: (value) => (
        <div className="text-sm">
          {value ? (
            <span className="text-green-600">
              {new Date(value).toLocaleDateString()}
            </span>
          ) : (
            <span className="text-gray-400">Not set</span>
          )}
        </div>
      ),
    },
    {
      key: "id",
      header: "School ID",
      render: (value) => (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
          #{value}
        </span>
      ),
    },
  ];

  const tableActions: TableActionConfig<School>[] = [
    {
      type: "edit",
      icon: "lucide:pen-line",
      color: "#F6B827",
      title: "Edit School",
      onClick: (row) => onEdit(row),
    },
    {
      type: "custom",
      icon: "mdi:image-plus",
      color: "#10B981",
      title: "Upload Logo",
      onClick: (row) => {
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "image/*";
        input.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            onLogoUpload(row.id, file);
          }
        };
        input.click();
      },
    },
    ...(onSignatureUpload
      ? [
          {
            type: "custom" as const,
            icon: "mdi:signature-freehand",
            color: "#8B5CF6",
            title: "Upload Principal Signature",
            onClick: (row: School) => onSignatureUpload(row),
          },
        ]
      : []),
    ...(onValidityDateUpdate
      ? [
          {
            type: "custom" as const,
            icon: "mdi:calendar-edit",
            color: "#F59E0B",
            title: "Update Validity Date",
            onClick: (row: School) => onValidityDateUpdate(row),
          },
        ]
      : []),
    {
      type: "delete",
      icon: "lucide:trash-2",
      color: "#EF4444",
      title: "Delete School",
      onClick: (row) => onDelete(row),
    },
  ];
  const EmptyState = () => (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">🏫</div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">No Schools Yet</h3>
      <p className="text-gray-600 mb-4">
        Create your first school to get started with the ID card system.
      </p>
      <button
        onClick={onCreateFirst}
        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
      >
        Create First School
      </button>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Schools ({schools.length})
          </h2>
          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode("grid")}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === "grid"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <svg
                  className="w-4 h-4 inline mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                Grid
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === "list"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <svg
                  className="w-4 h-4 inline mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                List
              </button>
            </div>
          </div>
        </div>

        {schools.length > 0 ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {schools.map((school) => (
                <SchoolCard
                  key={school.id}
                  school={school}
                  uploadingLogo={uploadingLogo === school.id}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onLogoUpload={onLogoUpload}
                  onSignatureUpload={onSignatureUpload}
                  onValidityDateUpdate={onValidityDateUpdate}
                />
              ))}
            </div>
          ) : (
            <MasterTable
              data={schools.map(transformSchoolToTableData)}
              columns={tableColumns}
              loading={false}
              actions={tableActions}
              showSelectAll={false}
              showActions={true}
              emptyStateMessage="No schools found"
              emptyStateIcon="mdi:school-outline"
              sortable={true}
              className="mt-4"
            />
          )
        ) : (
          <EmptyState />
        )}
      </div>
    </div>
  );
};

export default SchoolsList;
