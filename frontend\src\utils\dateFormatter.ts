/**
 * Date formatting utilities for consistent YYYY/MM/DD format across the application
 */

/**
 * Format a date to YYYY/MM/DD format
 * @param date - Date object, date string, or null/undefined
 * @returns Formatted date string in YYYY/MM/DD format or empty string if invalid
 */
export const formatDateYMD = (date: Date | string | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return '';
    }
    
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    
    return `${year}/${month}/${day}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Format a date for display with fallback text
 * @param date - Date object, date string, or null/undefined
 * @param fallback - Fallback text to display if date is invalid (default: "N/A")
 * @returns Formatted date string or fallback text
 */
export const formatDateYMDWithFallback = (
  date: Date | string | null | undefined, 
  fallback: string = "N/A"
): string => {
  const formatted = formatDateYMD(date);
  return formatted || fallback;
};

/**
 * Format validity date with default fallback
 * @param date - Date object, date string, or null/undefined
 * @param defaultValidity - Default validity date to use if date is invalid
 * @returns Formatted date string or default validity
 */
export const formatValidityDate = (
  date: Date | string | null | undefined,
  defaultValidity: string = "2083/01/15"
): string => {
  const formatted = formatDateYMD(date);
  return formatted || defaultValidity;
};
