const express = require("express");
const { body, validationResult } = require("express-validator");
const { Op } = require("sequelize");
const { Person, School } = require("../models");
const {
  csvUpload,
  photoUpload,
  handleMulterError,
} = require("../middleware/upload");
const { processCsvFile } = require("../utils/csvProcessor");
const ImageProcessor = require("../utils/imageProcessor");
const fs = require("fs");
const path = require("path");

// Import utility functions for person ID generation
const generateRandomPersonId = (type, schoolId) => {
  const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0"); // 3-digit random number
  const typePrefix =
    type === "student" ? "STU" : type === "staff" ? "STF" : "NTC";
  return `${typePrefix}${schoolId}${timestamp}${random}`;
};

const generateUniquePersonId = async (type, schoolId) => {
  let personId;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  while (!isUnique && attempts < maxAttempts) {
    personId = generateRandomPersonId(type, schoolId);

    // Check if this person_id already exists in this school
    const existingPerson = await Person.findOne({
      where: {
        person_id: personId,
        school_id: schoolId,
      },
    });

    if (!existingPerson) {
      isUnique = true;
    }
    attempts++;
  }

  if (!isUnique) {
    throw new Error(
      `Failed to generate unique person ID after ${maxAttempts} attempts`
    );
  }

  return personId;
};

const router = express.Router();

// Create new person
router.post(
  "/",
  [
    body("name").notEmpty().withMessage("Name is required"),
    body("type")
      .isIn(["student", "staff", "non_teaching"])
      .withMessage("Invalid person type"),
    body("person_id")
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage("Person ID must not exceed 50 characters if provided"),
    body("school_id").isInt().withMessage("Valid school ID is required"),
    // Conditional validation for students
    body("class")
      .if(body("type").equals("student"))
      .notEmpty()
      .withMessage("Class is required for students"),
    body("roll_number")
      .optional()
      .isString()
      .withMessage("Roll number must be a string"),
    body("designation")
      .if(body("type").isIn(["staff", "non_teaching"]))
      .optional()
      .isLength({ max: 100 })
      .withMessage("Designation must not exceed 100 characters"),
    body("parents_name")
      .optional()
      .isString()
      .withMessage("Parent's name must be a string"),
    body("section")
      .optional()
      .isString()
      .withMessage("Section must be a string"),
    body("address")
      .optional()
      .isString()
      .withMessage("Address must be a string"),

    body("date_of_birth")
      .optional()
      .isISO8601()
      .withMessage("Date of birth must be a valid date (YYYY-MM-DD)")
      .custom((value) => {
        if (value) {
          const birthDate = new Date(value);
          const today = new Date();
          const maxAge = new Date();
          maxAge.setFullYear(today.getFullYear() - 100);

          if (birthDate > today) {
            throw new Error("Date of birth cannot be in the future");
          }
          if (birthDate < maxAge) {
            throw new Error("Date of birth cannot be more than 100 years ago");
          }
        }
        return true;
      }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation failed",
          errors: errors.array().map((err) => err.msg),
        });
      }

      const {
        name,
        type,
        person_id,
        school_id,
        class: personClass,
        section,
        roll_number,
        parents_name,
        contact_no,
        department,
        designation,
        address,
      } = req.body;

      // Check if school exists
      const school = await School.findByPk(school_id);
      if (!school) {
        return res.status(400).json({ error: "Invalid school ID" });
      }

      // Generate person_id if not provided
      let finalPersonId = person_id;
      if (!finalPersonId || finalPersonId.trim() === "") {
        finalPersonId = await generateUniquePersonId(type, school_id);
        console.log(`Generated person_id: ${finalPersonId} for ${name}`);
      } else {
        // Check if provided person_id is unique
        const existingPerson = await Person.findOne({
          where: {
            person_id: finalPersonId,
            school_id: school_id,
          },
        });
        if (existingPerson) {
          return res
            .status(400)
            .json({ error: "Person ID already exists in this school" });
        }
      }

      // Create person
      const person = await Person.create({
        name,
        type,
        person_id: finalPersonId,
        school_id,
        class: personClass,
        section,
        roll_number,
        parents_name,
        contact_no,
        department,
        designation,
        address,
      });

      // Return created person with school info
      const createdPerson = await Person.findByPk(person.id, {
        include: [
          {
            model: School,
            attributes: ["id", "name", "address", "phone", "email"],
          },
        ],
      });

      res.status(201).json(createdPerson);
    } catch (error) {
      console.error("Error creating person:", error);
      if (error.name === "SequelizeUniqueConstraintError") {
        return res.status(400).json({ error: "Person ID must be unique" });
      }
      res.status(500).json({ error: "Failed to create person" });
    }
  }
);

// Upload CSV file and process
router.post(
  "/upload-csv",
  csvUpload.single("csv"),
  handleMulterError,
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No CSV file provided" });
      }

      const { school_id } = req.body;
      const result = await processCsvFile(
        req.file.path,
        req.file.filename,
        school_id
      );

      // Clean up uploaded file
      fs.unlinkSync(req.file.path);

      res.json({
        success: result.success,
        processed: result.processed,
        errors: result.errors,
        batchId: result.batchId,
      });
    } catch (error) {
      res.status(500).json({ error: "Failed to process CSV file" });
    }
  }
);

// Upload photos for persons
router.post(
  "/upload-photos",
  photoUpload.array("photos"),
  handleMulterError,
  async (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ error: "No photo files provided" });
      }

      const results = [];

      for (const file of req.files) {
        try {
          // Extract person_id from filename (expected format: personId_originalname)
          const personId = path.basename(
            file.originalname,
            path.extname(file.originalname)
          );

          // Find person by ID
          const person = await Person.findOne({
            where: { person_id: personId },
          });

          if (!person) {
            results.push({
              filename: file.originalname,
              success: false,
              error: `Person with ID ${personId} not found`,
            });
            continue;
          }

          // Compress and process image
          const processedImagePath = path.join(
            path.dirname(file.path),
            `processed_${file.filename}`
          );

          console.log(`Processing ${file.originalname}`);

          // Copy file to processed location without compression
          fs.copyFileSync(file.path, processedImagePath);

          // Update person record with photo path
          await person.update({ photo_path: `processed_${file.filename}` });

          // Delete original file
          fs.unlinkSync(file.path);

          results.push({
            filename: file.originalname,
            success: true,
            personId: personId,
          });
        } catch (error) {
          results.push({
            filename: file.originalname,
            success: false,
            error: error.message,
          });
        }
      }

      res.json({ results });
    } catch (error) {
      res.status(500).json({ error: "Failed to process photos" });
    }
  }
);

// Get all persons with optional filtering
router.get("/", async (req, res) => {
  try {
    const {
      type,
      search,
      school_id,
      page = 1,
      limit = 1000, // Increased default limit to handle more students
      class: classFilter,
      section,
    } = req.query;

    const whereClause = {};

    if (type && ["student", "staff", "non_teaching"].includes(type)) {
      whereClause.type = type;
    }

    if (search) {
      whereClause.name = { [Op.iLike]: `%${search}%` };
    }

    if (school_id) {
      whereClause.school_id = school_id;
    }

    if (classFilter) {
      whereClause.class = classFilter;
    }

    if (section) {
      whereClause.section = section;
    }

    // Ensure reasonable limits to prevent performance issues
    const parsedLimit = Math.min(Number(limit), 5000); // Max 5000 records at once
    const parsedPage = Math.max(Number(page), 1); // Minimum page 1

    const offset = (parsedPage - 1) * parsedLimit;

    const { count, rows } = await Person.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: School,
          attributes: ["id", "name", "address", "phone", "email"],
        },
      ],
      limit: parsedLimit,
      offset: offset,
      order: [["created_at", "DESC"]],
    });

    res.json({
      persons: rows,
      total: count,
      page: parsedPage,
      pages: Math.ceil(count / parsedLimit),
    });
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch persons" });
  }
});

// Get single person by ID
router.get("/:id", async (req, res) => {
  try {
    const person = await Person.findByPk(req.params.id, {
      include: [
        {
          model: School,
          attributes: ["id", "name", "address", "phone", "email", "logo_path"],
        },
      ],
    });

    if (!person) {
      return res.status(404).json({ error: "Person not found" });
    }

    res.json(person);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch person" });
  }
});

// Update person
router.put(
  "/:id",
  [
    body("name").notEmpty().withMessage("Name is required"),
    body("type")
      .isIn(["student", "staff", "non_teaching"])
      .withMessage("Invalid type"),
    body("person_id").notEmpty().withMessage("Person ID is required"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const person = await Person.findByPk(req.params.id);

      if (!person) {
        return res.status(404).json({ error: "Person not found" });
      }

      await person.update(req.body);
      res.json(person);
    } catch (error) {
      res.status(500).json({ error: "Failed to update person" });
    }
  }
);

// Delete all persons for a specific school (must be before /:id routes)
router.delete("/school/:schoolId", async (req, res) => {
  try {
    const { schoolId } = req.params;

    // Verify school exists
    const school = await School.findByPk(schoolId);
    if (!school) {
      return res.status(404).json({ error: "School not found" });
    }

    // Find all persons for this school
    const persons = await Person.findAll({
      where: { school_id: schoolId },
    });

    if (persons.length === 0) {
      return res.json({
        message: "No persons found for this school",
        deletedCount: 0,
        deletedPhotos: 0,
        schoolName: school.name,
      });
    }

    // Delete all photo files for these persons
    let deletedPhotos = 0;
    for (const person of persons) {
      if (person.photo_path) {
        const photoPath = path.join(
          __dirname,
          "../../uploads/photos",
          person.photo_path
        );
        if (fs.existsSync(photoPath)) {
          try {
            fs.unlinkSync(photoPath);
            deletedPhotos++;
          } catch (photoError) {
            console.warn(`Failed to delete photo: ${photoPath}`, photoError);
          }
        }
      }
    }

    // Delete all persons for this school
    const deletedCount = await Person.destroy({
      where: { school_id: schoolId },
    });

    res.json({
      message: `Successfully deleted all data for ${school.name}`,
      deletedCount,
      deletedPhotos,
      schoolName: school.name,
    });
  } catch (error) {
    console.error("Error deleting school data:", error);
    res.status(500).json({ error: "Failed to delete school data" });
  }
});

// Upload photo for a specific person
router.post(
  "/:id/photo",
  photoUpload.single("photo"),
  handleMulterError,
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No photo file provided" });
      }

      // Find person by ID
      const person = await Person.findByPk(req.params.id);

      if (!person) {
        return res.status(404).json({ error: "Person not found" });
      }

      // Validate uploaded image
      const validation = await ImageProcessor.validateImage(req.file.path);
      if (!validation.valid) {
        // Clean up uploaded file
        fs.unlinkSync(req.file.path);
        return res.status(400).json({
          error: "Invalid image file",
          details: validation.errors,
        });
      }

      // Delete existing photo if it exists
      if (person.photo_path) {
        const existingPhotoPath = path.join(
          __dirname,
          "../../uploads/photos",
          person.photo_path
        );
        if (fs.existsSync(existingPhotoPath)) {
          fs.unlinkSync(existingPhotoPath);
        }
      }

      // Process image with optimization
      const processedImagePath = path.join(
        path.dirname(req.file.path),
        `processed_${req.file.filename}`
      );

      console.log(`Processing image: ${req.file.filename}`);

      // Parse processing options from request body if provided
      const processingOptions = {
        width: 800,
        height: 800,
        quality: 90,
        format: "jpeg",
      };

      // If crop data is provided, parse it
      if (req.body.cropData) {
        try {
          const cropData = JSON.parse(req.body.cropData);
          processingOptions.crop = cropData;
        } catch (e) {
          console.warn("Invalid crop data provided:", e.message);
        }
      }

      // If filter data is provided, parse it
      if (req.body.filterData) {
        try {
          const filterData = JSON.parse(req.body.filterData);
          processingOptions.brightness = filterData.brightness || 0;
          processingOptions.contrast = filterData.contrast || 0;
          processingOptions.saturation = filterData.saturation || 0;
        } catch (e) {
          console.warn("Invalid filter data provided:", e.message);
        }
      }

      // Process the image
      const processingResult = await ImageProcessor.processImage(
        req.file.path,
        processedImagePath,
        processingOptions
      );

      // Create thumbnail
      const thumbnailPath = path.join(
        path.dirname(req.file.path),
        `thumb_${req.file.filename}`
      );
      await ImageProcessor.createThumbnail(
        processedImagePath,
        thumbnailPath,
        150
      );

      // Update person record with photo path
      await person.update({ photo_path: `processed_${req.file.filename}` });

      // Delete original uploaded file
      fs.unlinkSync(req.file.path);

      res.json({
        success: true,
        message: "Photo uploaded and processed successfully",
        person: person,
        processing: {
          originalDimensions: processingResult.originalDimensions,
          processedDimensions: processingResult.processedDimensions,
          compressionRatio: processingResult.compressionRatio,
          format: processingResult.format,
        },
      });
    } catch (error) {
      console.error("Photo upload error:", error);

      // Clean up files on error
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({ error: "Failed to upload photo" });
    }
  }
);

// Delete person
router.delete("/:id", async (req, res) => {
  try {
    const person = await Person.findByPk(req.params.id);

    if (!person) {
      return res.status(404).json({ error: "Person not found" });
    }

    // Delete photo file if exists
    if (person.photo_path) {
      const photoPath = path.join(
        __dirname,
        "../../uploads/photos",
        person.photo_path
      );
      if (fs.existsSync(photoPath)) {
        fs.unlinkSync(photoPath);
      }
    }

    await person.destroy();
    res.json({ message: "Person deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: "Failed to delete person" });
  }
});

module.exports = router;
