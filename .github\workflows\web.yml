name: CI/CD for School Card App Frontend (VPS)
on:
  push:
    branches:
      - main
    paths:
      - "frontend/**"
      - ".github/workflows/web.yml"

env:
  NODE_VERSION: 20

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: "./frontend/package-lock.json"

      - name: Install dependencies
        working-directory: ./frontend
        run: |
          rm -rf node_modules
          npm ci

      - name: Build React Application
        working-directory: ./frontend
        run: |
          npm run build

      - name: Create Web Production Bundle
        working-directory: ./frontend
        run: |
          tar -czf ../dist-web.tar.gz build

      - name: Deploy Web to VPS
        if: github.ref == 'refs/heads/main'
        env:
          VPS_HOST: ${{ secrets.VPS_HOST }}
          VPS_USER: ${{ secrets.VPS_USER }}
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H -p 72 $VPS_HOST >> ~/.ssh/known_hosts

          # Copy web bundle
          scp -i ~/.ssh/deploy_key -P 72 dist-web.tar.gz $VPS_USER@$VPS_HOST:/tmp/

                    ssh -i ~/.ssh/deploy_key -p 72 $VPS_USER@$VPS_HOST << 'EOF'
            set -e

            echo "Deploying School Card App Frontend..."

            # Create web directory and extract files
            mkdir -p "/home/<USER>/school-card-app"
            tar -xzf /tmp/dist-web.tar.gz -C "/home/<USER>/school-card-app" --strip-components=1
            rm /tmp/dist-web.tar.gz

            # Set proper permissions
            chown -R www-data:www-data "/home/<USER>/school-card-app" 2>/dev/null || echo "Could not set www-data ownership"
            chmod -R 755 "/home/<USER>/school-card-app"

            echo "Frontend deployment completed successfully!"

            # Trigger backend migration after frontend deployment
            echo "Triggering database migrations on backend container..."

            # Check if container is running
            if docker ps | grep -q "school-card-backend-container"; then
              echo "Backend container found, running migrations..."

              # Run all available migrations
              echo "Checking available migration files in container..."
              docker exec school-card-backend-container ls -la *.js | grep migration || echo "No migration files found"

              echo "Running address migration..."
              docker exec school-card-backend-container node run-migration.js || echo "Address migration failed"

              echo "Running designation migration..."
              docker exec school-card-backend-container node run-designation-migration.js || echo "Designation migration failed"

              echo "Running date_of_birth migration..."
              docker exec school-card-backend-container node run-date-of-birth-migration.js || echo "Date of birth migration failed (file may not exist in current container)"

              echo "All migrations completed!"
            else
              echo "Backend container not found or not running"
              exit 1
            fi
          EOF

      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/deploy_key
          rm -f dist-web.tar.gz
