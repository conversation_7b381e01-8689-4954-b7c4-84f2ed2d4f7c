import React, { useState, useEffect } from "react";
import axios from "axios";
import { Person, personService } from "../services/personService";
import { Icon } from "@iconify/react";
import { EditablePersonAvatar } from "./common";
import { UploadProfileModal } from "./UploadProfileModal";
import ImageEditorModal from "./ImageEditor/ImageEditorModal";
import { useNotification } from "../contexts/NotificationContext";

const API_BASE_URL = process.env.REACT_APP_API_URL || "http://localhost:5000";

interface EditPersonModalProps {
  person: Person | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const EditPersonModal: React.FC<EditPersonModalProps> = ({
  person,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<Partial<Person>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Photo editing states
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [imageEditorOpen, setImageEditorOpen] = useState(false);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);

  const { showNotification } = useNotification();

  useEffect(() => {
    if (person) {
      setFormData({
        person_id: person.person_id,
        name: person.name,
        type: person.type,
        class: person.class || "",
        section: person.section || "",
        roll_number: person.roll_number || "",
        parents_name: person.parents_name || "",
        contact_no: person.contact_no || "",
        department: person.department || "",
        address: person.address || "",
        date_of_birth: person.date_of_birth || "",
      });
    }
  }, [person]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Photo editing handlers
  const handleUploadPhoto = () => {
    setUploadModalOpen(true);
  };

  const handleEditExistingImage = async () => {
    if (!person?.photo_path) {
      console.log("No photo path found, opening upload modal");
      handleUploadPhoto();
      return;
    }

    console.log("Attempting to edit existing image:", person.photo_path);
    console.log("API_BASE_URL:", API_BASE_URL);

    try {
      const photoUrl = `${API_BASE_URL}/api/cards/photos/${
        person.photo_path
      }?t=${Date.now()}`;
      console.log("Fetching photo from:", photoUrl);

      const response = await fetch(photoUrl, {
        method: "GET",
        headers: {
          Accept: "image/*",
        },
      });

      console.log("Response status:", response.status);
      console.log(
        "Response headers:",
        Object.fromEntries(response.headers.entries())
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch image: ${response.status} ${response.statusText}`
        );
      }

      const blob = await response.blob();
      console.log("Blob size:", blob.size, "type:", blob.type);

      if (blob.size === 0) {
        throw new Error("Received empty image data");
      }

      const file = new File([blob], `${person.person_id}_photo.jpg`, {
        type: blob.type || "image/jpeg",
      });

      console.log("Created file:", file.name, "size:", file.size);
      setSelectedImageFile(file);
      setImageEditorOpen(true);
      console.log("Image editor should now be open");
    } catch (error: any) {
      console.error("Error fetching image for editing:", error);
      showNotification(
        `Failed to load image for editing: ${error.message || "Unknown error"}`,
        "error"
      );
      // Fallback to upload modal
      handleUploadPhoto();
    }
  };

  const handlePhotoClick = () => {
    if (person?.photo_path) {
      handleEditExistingImage();
    } else {
      handleUploadPhoto();
    }
  };

  const handlePhotoModalClose = () => {
    setUploadModalOpen(false);
    setImageEditorOpen(false);
    setSelectedImageFile(null);
  };

  const handlePhotoSuccess = () => {
    handlePhotoModalClose();
    onSuccess(); // Refresh the parent component
  };

  const handleImageEditorSave = async (processedFile: File) => {
    if (!person) return;

    try {
      const formData = new FormData();
      formData.append("photo", processedFile);

      await personService.uploadPhoto(person.id, formData);
      showNotification("Photo updated successfully!", "success");
      handlePhotoSuccess();
    } catch (error: any) {
      console.error("Error updating photo:", error);
      showNotification(
        error.response?.data?.error || "Failed to update photo",
        "error"
      );
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!person) return;

    setLoading(true);
    setError(null);

    try {
      await axios.put(`${API_BASE_URL}/api/persons/${person.id}`, formData);
      onSuccess();
      onClose();
    } catch (error: any) {
      setError(
        error.response?.data?.error || "Failed to update person information"
      );
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !person) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Edit Person</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {/* Photo Section */}
        <div className="mb-6 flex flex-col items-center">
          <div className="relative">
            <EditablePersonAvatar
              person={person}
              size="lg"
              showEditIcon={true}
              onEditClick={handlePhotoClick}
              editIconStyle="badge"
              className="cursor-pointer"
            />
          </div>
          <button
            type="button"
            onClick={handlePhotoClick}
            className="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center gap-1"
          >
            <Icon icon="mdi:camera" className="h-4 w-4" />
            {person?.photo_path ? "Change Photo" : "Add Photo"}
          </button>

          {/* Debug button - only in development */}
          {process.env.NODE_ENV === "development" && person?.photo_path && (
            <button
              type="button"
              onClick={async () => {
                const testUrl = `${API_BASE_URL}/api/cards/photos/${
                  person.photo_path
                }?t=${Date.now()}`;
                console.log("Testing photo URL:", testUrl);
                try {
                  const response = await fetch(testUrl);
                  console.log(
                    "Test response:",
                    response.status,
                    response.statusText
                  );
                  if (response.ok) {
                    const blob = await response.blob();
                    console.log("Test blob:", blob.size, blob.type);
                  }
                } catch (error) {
                  console.error("Test error:", error);
                }
              }}
              className="mt-1 text-xs text-gray-500 hover:text-gray-700"
            >
              🔍 Test Photo URL
            </button>
          )}
          <p className="mt-1 text-xs text-gray-500 text-center">
            {person?.photo_path
              ? "Click the blue camera icon or button above to edit/replace the photo"
              : "Click the blue camera icon or button above to upload a photo"}
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Person ID
            </label>
            <input
              type="text"
              name="person_id"
              value={formData.person_id || ""}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name || ""}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              name="type"
              value={formData.type || ""}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="student">Student</option>
              <option value="staff">Staff</option>
              <option value="non_teaching">Non Teaching</option>
            </select>
          </div>

          {formData.type === "student" && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Class
                </label>
                <input
                  type="text"
                  name="class"
                  value={formData.class || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Section{" "}
                  <span className="text-gray-400 text-xs">(Optional)</span>
                </label>
                <input
                  type="text"
                  name="section"
                  value={formData.section || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Roll Number{" "}
                  <span className="text-gray-400 text-xs">(Optional)</span>
                </label>
                <input
                  type="text"
                  name="roll_number"
                  value={formData.roll_number || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Parent's Name{" "}
                  <span className="text-gray-400 text-xs">(Optional)</span>
                </label>
                <input
                  type="text"
                  name="parents_name"
                  value={formData.parents_name || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </>
          )}

          {(formData.type === "staff" || formData.type === "non_teaching") && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department
              </label>
              <input
                type="text"
                name="department"
                value={formData.department || ""}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Contact Number
            </label>
            <input
              type="text"
              name="contact_no"
              value={formData.contact_no || ""}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Date of Birth Field - Only for students */}
          {formData.type === "student" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Birth{" "}
                <span className="text-gray-400 text-xs">(Optional)</span>
              </label>
              <input
                type="date"
                name="date_of_birth"
                value={formData.date_of_birth || ""}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                max={new Date().toISOString().split("T")[0]} // Prevent future dates
                min={
                  new Date(new Date().getFullYear() - 100, 0, 1)
                    .toISOString()
                    .split("T")[0]
                } // Prevent dates more than 100 years ago
              />
              <p className="mt-1 text-xs text-gray-500">
                This will be displayed on Layout 4 ID cards for students
              </p>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address <span className="text-gray-400 text-xs">(Optional)</span>
            </label>
            <textarea
              name="address"
              value={formData.address || ""}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter person's address (if different from school address)"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? "Updating..." : "Update Person"}
            </button>
          </div>
        </form>
      </div>

      {/* Photo Upload Modal */}
      {uploadModalOpen && person && (
        <UploadProfileModal
          person={person}
          isOpen={uploadModalOpen}
          onClose={handlePhotoModalClose}
          onSuccess={handlePhotoSuccess}
        />
      )}

      {/* Image Editor Modal */}
      {imageEditorOpen && selectedImageFile && (
        <ImageEditorModal
          file={selectedImageFile}
          isOpen={imageEditorOpen}
          onClose={handlePhotoModalClose}
          onSave={handleImageEditorSave}
          aspectRatio={1} // Square aspect ratio for profile photos
          maxWidth={800}
          maxHeight={800}
        />
      )}

      {/* Debug info */}
      {process.env.NODE_ENV === "development" && (
        <div
          style={{
            position: "fixed",
            top: 10,
            right: 10,
            background: "rgba(0,0,0,0.8)",
            color: "white",
            padding: "10px",
            fontSize: "12px",
            zIndex: 9999,
          }}
        >
          <div>imageEditorOpen: {imageEditorOpen.toString()}</div>
          <div>selectedImageFile: {selectedImageFile ? "Yes" : "No"}</div>
          <div>uploadModalOpen: {uploadModalOpen.toString()}</div>
          <div>person.photo_path: {person?.photo_path || "None"}</div>
        </div>
      )}
    </div>
  );
};
