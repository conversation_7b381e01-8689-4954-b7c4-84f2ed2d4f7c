import { StudentData } from "./types";
import ProfileImage from "../../../common/ProfileImage";
import { hasValidValue } from "../../utils/fieldValidation";

interface StudentInfoProps {
  data: StudentData;
}

const StudentInfo = ({ data }: StudentInfoProps) => {
  const isStudent = data.personType === "student";
  const fieldConfig = data.fieldVisibilityConfig;

  return (
    <div className="relative z-10 pt-[46px] px-4 pb-2 flex flex-col h-full">
      {/* Student Photo - positioned in center */}
      <div className="flex justify-center mb-1">
        <div className="w-[90px] h-24 bg-green-500 border-2 border-white rounded-lg overflow-hidden flex items-center justify-center">
          <ProfileImage
            src={data.image}
            name={data.studentName}
            designation={data.personType}
            className="w-full h-full object-cover"
            shape="square"
            showInitials={true}
          />
        </div>
      </div>

      {/* Student Name - Large and prominent */}
      <div className="text-center mb-0">
        <h2 className="text-blue-800 font-bold w-full ml-[10%] font-['Ubuntu'] text-sm  uppercase tracking-tight  overflow-hidden ">
          {data.studentName}
        </h2>
      </div>

      {/* Student Details */}
      <div className="text-[9px] w-[80%] ml-[8%] mr-0 leading-relaxed space-y-0 flex-grow">
        {isStudent ? (
          <>
            {/* Class - Configurable */}
            {fieldConfig?.class !== false && hasValidValue(data.class) && (
              <div className="grid grid-cols-2 gap-1">
                <div className="text-end">
                  <span className="font-bold text-black font-['Ubuntu'] text-[9px] tracking-tight">
                    Class :
                  </span>
                </div>
                <div className="text-start">
                  <span className="text-[#234674] font-['Ubuntu'] font-medium text-[9px] tracking-tight">
                    {data.class}
                  </span>
                </div>
              </div>
            )}

            {/* Address - Configurable - Can wrap to two lines */}
            {fieldConfig?.address !== false && hasValidValue(data.address) && (
              <div className="grid grid-cols-2 gap-1">
                <div className="text-end">
                  <span className="font-bold text-black font-['Ubuntu'] text-[9px] tracking-tight">
                    Address :
                  </span>
                </div>
                <div className="text-start">
                  <span className="text-blue-600 font-['Ubuntu'] font-medium leading-tight text-[9px] tracking-tight">
                    {data.address}
                  </span>
                </div>
              </div>
            )}

            {/* Contact - Configurable */}
            {fieldConfig?.contact_no !== false &&
              hasValidValue(data.contactNo) && (
                <div className="grid grid-cols-2 gap-1">
                  <div className="text-end">
                    <span className="font-bold text-black font-['Ubuntu'] text-[9px] tracking-tight">
                      Contact :
                    </span>
                  </div>
                  <div className="text-start">
                    <span className="text-blue-600 font-['Ubuntu'] font-medium text-[9px] tracking-tight">
                      {data.contactNo}
                    </span>
                  </div>
                </div>
              )}

            {/* Date of Birth - Configurable */}
            {fieldConfig?.date_of_birth !== false &&
              hasValidValue(data.dateOfBirth) && (
                <div className="grid grid-cols-2 gap-1">
                  <div className="text-end">
                    <span className="font-bold text-black font-['Ubuntu'] text-[9px] tracking-tight">
                      DOB :
                    </span>
                  </div>
                  <div className="text-start">
                    <span className="text-blue-600 font-['Ubuntu'] font-medium text-[9px] tracking-tight">
                      {data.dateOfBirth
                        ? new Date(data.dateOfBirth).toLocaleDateString(
                            "en-CA",
                            {
                              year: "numeric",
                              month: "2-digit",
                              day: "2-digit",
                            }
                          )
                        : "N/A"}
                    </span>
                  </div>
                </div>
              )}

            {/* Parent's Name - Configurable */}
            {fieldConfig?.parents_name !== false &&
              hasValidValue(data.parentName) && (
                <div className="grid grid-cols-2 gap-1">
                  <div className="text-end">
                    <span className="font-bold text-black font-['Ubuntu'] text-[9px] tracking-tight">
                      Parent's Name :
                    </span>
                  </div>
                  <div className="text-start">
                    <span className="text-blue-600 font-['Ubuntu'] font-medium text-[9px] tracking-tight">
                      {data.parentName}
                    </span>
                  </div>
                </div>
              )}

            {/* Valid Upto - From footer */}

            <div className="text-center">
              <span className="font-normal text-red-600 font-['Ubuntu'] text-[9px] tracking-tight">
                Valid Upto : {data.validity || "2026/10/08"}
              </span>
            </div>
          </>
        ) : (
          <>
            {/* Staff specific fields */}
            {fieldConfig?.department !== false &&
              hasValidValue(data.department) && (
                <div className="grid grid-cols-2 gap-1">
                  <div className="text-end">
                    <span className="font-bold text-black font-['Ubuntu'] text-[9px] tracking-tight">
                      Department:
                    </span>
                  </div>
                  <div className="text-start">
                    <span className="text-blue-600 font-['Ubuntu'] font-medium text-[9px] tracking-tight">
                      {data.department}
                    </span>
                  </div>
                </div>
              )}

            {/* Contact - Configurable */}
            {fieldConfig?.contact_no !== false &&
              hasValidValue(data.contactNo) && (
                <div className="grid grid-cols-2 gap-1">
                  <div className="text-end">
                    <span className="font-bold text-black font-['Ubuntu'] text-[9px] tracking-tight">
                      Contact No.:
                    </span>
                  </div>
                  <div className="text-start">
                    <span className="text-blue-600 font-['Ubuntu'] font-medium text-[9px] tracking-tight">
                      {data.contactNo}
                    </span>
                  </div>
                </div>
              )}

            {/* Valid Upto - From footer */}
            <div className="grid grid-cols-2 gap-1 mt-4 mb-2">
              <div className="text-end">
                <span className="font-bold text-red-600 font-['Ubuntu'] text-[9px] tracking-tight">
                  Valid Upto : {data.validity || "2026/10/8"}
                </span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default StudentInfo;
